<!-- 基本信息 -->
<template>
  <div>
    <el-card>
      <CommonTitle class="mb10" title="基本信息" />
      <BaseDescriptions :list="infoList" :column="2">
        <template #links="{itemVal}">
          <FileIcons :list="itemVal"></FileIcons>
        </template>
      </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="合同协议方" />
      <BaseDescriptions :list="partyList" :column="2"> </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="其他信息" />
      <BaseDescriptions :list="otherList" :column="2">
        <template #links="{itemVal}">
          <FileIcons :list="itemVal"></FileIcons>
        </template>
      </BaseDescriptions>
      <div v-if="menuName === '采购框架协议申请'">
        <h4>付款条件</h4>
        <vxe-grid
          resizable
          align="center"
          :columns="columns1"
          :data="baseInfo.tableData"
        ></vxe-grid>
      </div>
      <div v-if="menuName === '固定资产采购合同申请'">
        <h4>付款条件</h4>
        <vxe-grid
          resizable
          align="center"
          :columns="columns2"
          :data="baseInfo.tableData"
        ></vxe-grid>
      </div>
    </el-card>

    <!-- 新的其他信息展示卡片 -->
    <el-card v-if="showNewOtherInfo">
      <CommonTitle
        class="mb10"
        :title="`${currentTypeConfig.name} - 详细信息`"
      />

      <!-- 基础字段展示 -->
      <BaseDescriptions
        :list="newOtherInfoList"
        :column="2"
        v-if="newOtherInfoList.length > 0"
      >
        <template #links="{itemVal}">
          <FileIcons :list="itemVal"></FileIcons>
        </template>
      </BaseDescriptions>

      <!-- 表格字段展示 -->
      <div
        v-for="tableField in currentTypeConfig.tableFields"
        :key="tableField.field"
        class="table-display-section"
      >
        <h4>{{ tableField.title }}</h4>
        <p v-if="tableField.description" class="table-description">
          {{ tableField.description }}
        </p>
        <vxe-grid
          resizable
          align="center"
          :columns="getDisplayTableColumns(tableField.tableType)"
          :data="baseInfo[tableField.field] || []"
          max-height="400px"
        ></vxe-grid>
      </div>
    </el-card>
  </div>
</template>

<script>
import FileIcons from "@/components/FileIcons/index.vue";
import CommonTitle from "@/components/commonTitle";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/infoArchive/contractAgreement/agreement.js";
import { OtherInfoDict } from "./config";
import { getConfigByType, getTableColumns } from "./otherInfoConfig.js";
export default {
  components: {
    CommonTitle,
    BaseDescriptions,
    FileIcons,
  },
  props: {
    contractId: { type: String, default: "" },
  },
  data() {
    return {
      columns1: [
        { title: "付款条件", field: "fktj" },
        { title: "付款比例（%）", field: "fkbl" },
      ],
      columns2: [
        { title: "付款条件", field: "fktj" },
        { title: "付款比例（%）", field: "fkbl" },
        { title: "付款金额", field: "fktj" },
        { title: "预计付款时间", field: "fktj" },
      ],
      infoList: [],
      partyList: [],
      baseInfo: {},
      menuName: "",
      contractType: "", // 合同协议类型
    };
  },
  created() {
    // this.getDetail();
  },
  computed: {
    otherList() {
      return OtherInfoDict[this.menuName]?.map((x) => {
        const val = this.baseInfo?.[x.value];
        return { ...x, value: x.formatter?.(val) || val };
      });
    },

    // 当前合同类型配置
    currentTypeConfig() {
      return getConfigByType(this.contractType);
    },

    // 是否显示新的其他信息卡片
    showNewOtherInfo() {
      return this.contractType && this.currentTypeConfig.name !== "未知类型";
    },

    // 新的其他信息列表
    newOtherInfoList() {
      if (!this.currentTypeConfig.displayFields) return [];

      return this.currentTypeConfig.displayFields.map((field) => ({
        title: field.title,
        value: this.baseInfo[field.value] || "",
        ...field,
      }));
    },
  },
  methods: {
    getDetail() {
      api.baseInfoDetail({ contractId: this.contractId }).then((res) => {
        this.baseInfo = res.data;
        this.menuName = res.data.contractTypeName;
        this.contractType = res.data.contractType; // 设置合同协议类型
        this.infoList = [
          {
            title: "运管申请单号",
            value: res.data?.omApplyNo,
            copy: true,
          },
          {
            title: "合同编码",
            value: res.data?.contractNo,
            copy: true,
          },
          {
            title: "变更合同&协议申请编号",
            value: res.data?.changeApplyNo,
            copy: true,
          },
          {
            title: "属性",
            value: res.data?.attributeName,
          },
          {
            title: "合同&协议名称",
            value: res.data?.contractName,
          },
          {
            title: "业务类型",
            value: res.data?.businessType,
          },
          {
            title: "合同&协议生效时间",
            value: res.data?.effectiveTime,
          },
          {
            title: "合同&协议失效时间",
            value: res.data?.expireTime,
          },
          {
            title: "备注",
            value: res.data?.remark,
          },
          {
            title: "附件",
            value: res.data?.attachment ? JSON.parse(res.data?.attachment) : [],
            slotName: "links",
          },
          {
            title: "合同协议状态",
            value: res.data?.status,
          },
          {
            title: "运管审批状态",
            value: res.data?.applyStatus,
          },
          {
            title: "场站名称",
            value: res.data?.stationName,
          },
          {
            title: "场站编码",
            value: res.data?.stationCode,
          },
          {
            title: "提交人",
            value: res.data?.submitUser,
          },
          {
            title: "提交时间",
            value: res.data?.submitTime,
          },
        ];
        this.partyList = [
          {
            title: "甲方",
            value: res.data?.partA,
          },
          {
            title: "乙方",
            value: res.data?.partB,
          },
          {
            title: "丙方",
            value: res.data?.partC,
            hidden: !res.data?.partC,
          },
          {
            title: "丁方",
            value: res.data?.partD,
            hidden: !res.data?.partD,
          },
          {
            title: "戊方",
            value: res.data?.partE,
            hidden: !res.data?.partE,
          },
        ];
      });
    },

    // 获取表格显示列配置（只读模式）
    getDisplayTableColumns(tableType) {
      const columns = getTableColumns(tableType);
      // 转换为只读显示列配置
      return columns.map((column) => ({
        title: column.title,
        field: column.field,
        width: column.width,
        // 移除编辑相关配置，只保留显示
        formatter: ({ cellValue }) => {
          // 根据字段类型格式化显示值
          if (column.element === "el-input-number") {
            return cellValue !== null && cellValue !== undefined
              ? cellValue
              : "";
          }
          return cellValue || "";
        },
      }));
    },
  },
};
</script>

<style lang="less" scoped>
.table-display-section {
  margin-top: 20px;

  h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .table-description {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #909399;
  }
}
</style>
