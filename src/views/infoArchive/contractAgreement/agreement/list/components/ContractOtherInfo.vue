<!-- 合同协议其他信息组件 -->
<template>
  <div class="contract-other-info">
    <!-- 基础表单字段 -->
    <DynamicForm
      ref="dynamicForm"
      :config="formConfig"
      :params="formData"
      :defaultColSpan="24"
      labelPosition="right"
      labelWidth="150px"
      v-if="formConfig.length > 0"
    />

    <!-- 表格字段 -->
    <div
      v-for="tableField in tableFields"
      :key="tableField.field"
      class="table-section"
    >
      <h4 class="table-title">{{ tableField.title }}</h4>
      <p class="table-description" v-if="tableField.description">
        {{ tableField.description }}
      </p>
      <StationConfigTable
        :ref="tableField.field"
        :columns="getTableColumns(tableField.tableType)"
        :value="formData[tableField.field] || []"
        @input="handleTableDataChange(tableField.field, $event)"
        showAddBtn
      />
    </div>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import StationConfigTable from "@/components/StationConfigTable/index.vue";
import {
  getConfigByType,
  getTableColumns,
  DICT_TYPES,
  INVESTMENT_TABLE_COLUMNS,
} from "./otherInfoConfig.js";

export default {
  name: "ContractOtherInfo",
  components: {
    StationConfigTable,
  },
  props: {
    // 合同协议类型
    contractType: {
      type: String,
      default: "",
    },
    // 表单数据，支持 v-model
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
      // 字典数据
      areaOptions: [],
      electricityBillingMethodOptions: [],
      hasLateFeeOptions: [],
      revenueModeOptions: [],
      agreementTypeOptions: [],
      // 防止无限循环的标志
      isUpdating: false,
    };
  },
  computed: {
    // 当前类型配置
    currentConfig() {
      return getConfigByType(this.contractType);
    },

    // 表单配置
    formConfig() {
      return this.currentConfig.formFields || [];
    },

    // 表格字段配置
    tableFields() {
      return this.currentConfig.tableFields || [];
    },
  },
  watch: {
    // 监听 contractType 变化，重新初始化数据
    contractType: {
      handler(newType, oldType) {
        console.log("contractType changed:", newType, "from:", oldType);
        if (newType !== oldType && newType) {
          this.initFormData();
        }
      },
      immediate: true,
    },

    // 监听 value 变化，同步到内部数据
    value: {
      handler(newValue) {
        console.log("value changed:", newValue);
        if (!this.isUpdating && newValue && typeof newValue === "object") {
          this.formData = { ...newValue };
        }
      },
      immediate: true,
      deep: true,
    },

    // 监听内部数据变化，触发 v-model 更新 - 暂时禁用
    // formData: {
    //   handler(newData, oldData) {
    //     console.log("formData changed - DISABLED");
    //   },
    //   deep: true,
    // },
  },
  created() {
    console.log("ContractOtherInfo created");
    this.loadDictData();
    // 不在 created 中调用 initFormData，让 watch 来处理
  },
  methods: {
    // 获取表格列配置
    getTableColumns(tableType) {
      console.log(
        "🔥 getTableColumns called with:",
        tableType,
        "at",
        new Date().toISOString()
      );

      // 添加调用栈信息来调试
      console.trace("getTableColumns call stack");

      const columns = getTableColumns(tableType);
      if (!columns || !Array.isArray(columns)) {
        console.warn("Invalid columns for tableType:", tableType);
        return [];
      }

      console.log("Processing", columns.length, "columns");

      // 动态设置字典数据
      return columns.map((column) => {
        const newColumn = { ...column };

        try {
          // 设置区域选项
          if (column.field === "area") {
            newColumn.props = {
              ...column.props,
              fetchSuggestions: this.queryAreaSearch,
            };
          }

          // 设置字典选项
          if (column.field === "electricityBillingMethod") {
            newColumn.props = {
              ...column.props,
              options: this.electricityBillingMethodOptions || [],
            };
          }

          if (column.field === "hasLateFee") {
            newColumn.props = {
              ...column.props,
              options: this.hasLateFeeOptions || [],
            };
          }

          if (column.field === "revenueMode") {
            newColumn.props = {
              ...column.props,
              options: this.revenueModeOptions || [],
            };
          }
        } catch (error) {
          console.error("Error processing column:", column.field, error);
        }

        return newColumn;
      });
    },

    // 初始化表单数据
    initFormData() {
      console.log("initFormData called, contractType:", this.contractType);

      // 如果没有合同类型，清空数据
      if (!this.contractType) {
        this.isUpdating = true;
        this.formData = {};
        this.$nextTick(() => {
          this.isUpdating = false;
        });
        return;
      }

      const config = this.currentConfig;
      console.log("config:", config);

      // 如果配置无效，不进行初始化
      if (!config || config.name === "未知类型") {
        console.warn("Invalid config for contractType:", this.contractType);
        return;
      }

      const initialData = {};

      // 初始化表单字段
      if (config.formFields && Array.isArray(config.formFields)) {
        config.formFields.forEach((field) => {
          initialData[field.field] = "";
        });
      }

      // 初始化表格字段
      if (config.tableFields && Array.isArray(config.tableFields)) {
        config.tableFields.forEach((field) => {
          initialData[field.field] = [];
        });
      }

      console.log("initialData:", initialData);

      // 合并现有数据并更新
      this.isUpdating = true;
      this.formData = {
        ...initialData,
        ...this.value,
      };

      this.$nextTick(() => {
        this.isUpdating = false;
        console.log("formData updated:", this.formData);
      });
    },

    // 加载字典数据
    loadDictData() {
      console.log("loadDictData called");

      // 检查 getDicts 方法是否存在
      if (typeof this.getDicts !== "function") {
        console.warn("getDicts method not found, using empty options");
        // 设置默认空数据
        this.areaOptions = [];
        this.electricityBillingMethodOptions = [];
        this.hasLateFeeOptions = [];
        this.revenueModeOptions = [];
        this.agreementTypeOptions = [];
        return;
      }

      console.log("开始加载字典数据");

      // 加载区域配置
      this.getDicts(DICT_TYPES.AREA)
        .then((response) => {
          this.areaOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load area options:", error);
          this.areaOptions = [];
        });

      // 加载电费计费方式
      this.getDicts(DICT_TYPES.ELECTRICITY_BILLING_METHOD)
        .then((response) => {
          this.electricityBillingMethodOptions = response.data || [];
        })
        .catch((error) => {
          console.error(
            "Failed to load electricity billing method options:",
            error
          );
          this.electricityBillingMethodOptions = [];
        });

      // 加载是否有滞纳金
      this.getDicts(DICT_TYPES.HAS_LATE_FEE)
        .then((response) => {
          this.hasLateFeeOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load late fee options:", error);
          this.hasLateFeeOptions = [];
        });

      // 加载收益模式
      this.getDicts(DICT_TYPES.REVENUE_MODE)
        .then((response) => {
          this.revenueModeOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load revenue mode options:", error);
          this.revenueModeOptions = [];
        });

      // 加载协议类型
      this.getDicts(DICT_TYPES.AGREEMENT_TYPE)
        .then((response) => {
          this.agreementTypeOptions = response.data || [];
          // 更新协议类型字段的选项
          this.updateAgreementTypeOptions();
        })
        .catch((error) => {
          console.error("Failed to load agreement type options:", error);
          this.agreementTypeOptions = [];
        });
    },

    // 更新协议类型选项
    updateAgreementTypeOptions() {
      const config = this.formConfig.find(
        (field) => field.field === "agreementType"
      );
      if (config && config.props) {
        config.props.options = this.agreementTypeOptions;
      }
    },

    // 区域搜索
    queryAreaSearch(queryString, cb) {
      const results = this.areaOptions
        .filter((item) => {
          return item.dictLabel
            .toLowerCase()
            .includes(queryString.toLowerCase());
        })
        .map((item) => ({
          value: item.dictLabel,
        }));
      cb(results);
    },

    // 处理表格数据变化 - 暂时禁用自动更新
    handleTableDataChange(fieldName, newData) {
      console.log(
        "handleTableDataChange called - DISABLED AUTO UPDATE:",
        fieldName,
        newData
      );
      // 暂时不更新 formData，避免循环
      // this.$set(this.formData, fieldName, newData);
    },

    // 表单验证
    async validate() {
      let isValid = true;

      // 验证动态表单
      if (this.$refs.dynamicForm) {
        try {
          await this.$refs.dynamicForm.validate();
        } catch (error) {
          isValid = false;
        }
      }

      // 验证表格
      for (const tableField of this.tableFields) {
        const tableRef = this.$refs[tableField.field];
        if (tableRef && tableRef[0]) {
          try {
            await tableRef[0].validate();
          } catch (error) {
            isValid = false;
          }
        }
      }

      return isValid;
    },

    // 重置表单
    resetForm() {
      this.initFormData();

      // 重置动态表单
      if (this.$refs.dynamicForm) {
        this.$refs.dynamicForm.resetFields();
      }

      // 重置表格
      this.tableFields.forEach((tableField) => {
        const tableRef = this.$refs[tableField.field];
        if (tableRef && tableRef[0]) {
          tableRef[0].resetForm();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contract-other-info {
  .table-section {
    margin-top: 20px;

    .table-title {
      margin: 0 0 10px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .table-description {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #909399;
    }
  }
}
</style>
