<!-- 合同协议其他信息组件 -->
<template>
  <div class="contract-other-info">
    <!-- 基础表单字段 -->
    <DynamicForm
      ref="dynamicForm"
      :config="formConfig"
      :params="formData"
      :defaultColSpan="24"
      labelPosition="right"
      labelWidth="150px"
      v-if="formConfig.length > 0"
    />

    <!-- 表格字段 -->
    <div
      v-for="tableField in tableFields"
      :key="tableField.field"
      class="table-section"
    >
      <h4 class="table-title">{{ tableField.title }}</h4>
      <p class="table-description" v-if="tableField.description">
        {{ tableField.description }}
      </p>
      <StationConfigTable
        :ref="tableField.field"
        :columns="getTableColumns(tableField.tableType)"
        v-model="formData[tableField.field]"
        showAddBtn
      />
    </div>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import StationConfigTable from "@/components/StationConfigTable/index.vue";
import {
  getConfigByType,
  getTableColumns,
  DICT_TYPES,
  INVESTMENT_TABLE_COLUMNS,
} from "./otherInfoConfig.js";

export default {
  name: "ContractOtherInfo",
  components: {
    StationConfigTable,
  },
  props: {
    // 合同协议类型
    contractType: {
      type: String,
      default: "",
    },
    // 表单数据，支持 v-model
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
      // 字典数据
      areaOptions: [],
      electricityBillingMethodOptions: [],
      hasLateFeeOptions: [],
      revenueModeOptions: [],
      agreementTypeOptions: [],
    };
  },
  computed: {
    // 当前类型配置
    currentConfig() {
      return getConfigByType(this.contractType);
    },

    // 表单配置
    formConfig() {
      return this.currentConfig.formFields || [];
    },

    // 表格字段配置
    tableFields() {
      return this.currentConfig.tableFields || [];
    },
  },
  watch: {
    // 监听 contractType 变化，重新初始化数据
    contractType: {
      handler(newType) {
        console.log("newType", newType);
        this.initFormData();
      },
      immediate: true,
    },

    // 监听 value 变化，同步到内部数据
    value: {
      handler(newValue) {
        if (newValue && Object.keys(newValue).length > 0) {
          this.formData = { ...newValue };
        }
      },
      immediate: true,
      deep: true,
    },

    // 监听内部数据变化，触发 v-model 更新
    formData: {
      handler(newData) {
        this.$emit("input", newData);
      },
      deep: true,
    },
  },
  created() {
    this.loadDictData();
    this.initFormData();
  },
  methods: {
    // 获取表格列配置
    getTableColumns(tableType) {
      const columns = getTableColumns(tableType);
      // 动态设置字典数据
      return columns.map((column) => {
        const newColumn = { ...column };

        // 设置区域选项
        if (column.field === "area") {
          newColumn.props = {
            ...column.props,
            fetchSuggestions: this.queryAreaSearch,
          };
        }

        // 设置字典选项
        if (column.field === "electricityBillingMethod") {
          newColumn.props = {
            ...column.props,
            options: this.electricityBillingMethodOptions,
          };
        }

        if (column.field === "hasLateFee") {
          newColumn.props = {
            ...column.props,
            options: this.hasLateFeeOptions,
          };
        }

        if (column.field === "revenueMode") {
          newColumn.props = {
            ...column.props,
            options: this.revenueModeOptions,
          };
        }

        return newColumn;
      });
    },

    // 初始化表单数据
    initFormData() {
      console.log("contractType", this.contractType);
      if (!this.contractType) return;

      const config = this.currentConfig;
      const initialData = {};
      console.log("config", config);
      // 初始化表单字段
      config.formFields?.forEach((field) => {
        initialData[field.field] = "";
      });

      // 初始化表格字段
      config.tableFields?.forEach((field) => {
        initialData[field.field] = [];
      });
      console.log("initialData", initialData);

      // 合并现有数据
      this.formData = {
        ...initialData,
        ...this.value,
      };
      console.log("formData", this.formData);
    },

    // 加载字典数据
    loadDictData() {
      // 加载区域配置
      this.getDicts(DICT_TYPES.AREA).then((response) => {
        this.areaOptions = response.data || [];
      });

      // 加载电费计费方式
      this.getDicts(DICT_TYPES.ELECTRICITY_BILLING_METHOD).then((response) => {
        this.electricityBillingMethodOptions = response.data || [];
      });

      // 加载是否有滞纳金
      this.getDicts(DICT_TYPES.HAS_LATE_FEE).then((response) => {
        this.hasLateFeeOptions = response.data || [];
      });

      // 加载收益模式
      this.getDicts(DICT_TYPES.REVENUE_MODE).then((response) => {
        this.revenueModeOptions = response.data || [];
      });

      // 加载协议类型
      this.getDicts(DICT_TYPES.AGREEMENT_TYPE).then((response) => {
        this.agreementTypeOptions = response.data || [];

        // 更新协议类型字段的选项
        this.updateAgreementTypeOptions();
      });
    },

    // 更新协议类型选项
    updateAgreementTypeOptions() {
      const config = this.formConfig.find(
        (field) => field.field === "agreementType"
      );
      if (config && config.props) {
        config.props.options = this.agreementTypeOptions;
      }
    },

    // 区域搜索
    queryAreaSearch(queryString, cb) {
      const results = this.areaOptions
        .filter((item) => {
          return item.dictLabel
            .toLowerCase()
            .includes(queryString.toLowerCase());
        })
        .map((item) => ({
          value: item.dictLabel,
        }));
      cb(results);
    },

    // 表单验证
    async validate() {
      let isValid = true;

      // 验证动态表单
      if (this.$refs.dynamicForm) {
        try {
          await this.$refs.dynamicForm.validate();
        } catch (error) {
          isValid = false;
        }
      }

      // 验证表格
      for (const tableField of this.tableFields) {
        const tableRef = this.$refs[tableField.field];
        if (tableRef && tableRef[0]) {
          try {
            await tableRef[0].validate();
          } catch (error) {
            isValid = false;
          }
        }
      }

      return isValid;
    },

    // 重置表单
    resetForm() {
      this.initFormData();

      // 重置动态表单
      if (this.$refs.dynamicForm) {
        this.$refs.dynamicForm.resetFields();
      }

      // 重置表格
      this.tableFields.forEach((tableField) => {
        const tableRef = this.$refs[tableField.field];
        if (tableRef && tableRef[0]) {
          tableRef[0].resetForm();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.contract-other-info {
  .table-section {
    margin-top: 20px;

    .table-title {
      margin: 0 0 10px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .table-description {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #909399;
    }
  }
}
</style>
