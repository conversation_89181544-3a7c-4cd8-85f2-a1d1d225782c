<!-- 合同协议其他信息组件 - 简化版本 -->
<template>
  <div class="contract-other-info-simple">
    <div v-if="!contractType" class="no-type-message">
      请先选择合同协议类型
    </div>

    <div v-else>
      <h4>合同协议类型: {{ contractType }}</h4>
      <p>配置名称: {{ currentConfig.name }}</p>

      <!-- 基础表单字段 - 使用 DynamicForm -->
      <div v-if="formFields.length > 0" class="form-section">
        <h5>基础信息 (DynamicForm)</h5>
        <DynamicForm
          ref="dynamicForm"
          :config="formFields"
          :params="formData"
          :defaultColSpan="24"
          labelPosition="right"
          labelWidth="150px"
        />
      </div>

      <!-- 基础表单字段 - 原始版本作为对比 -->
      <div v-if="formFields.length > 0" class="form-section">
        <h5>基础信息 (原始表单)</h5>
        <el-form :model="formData" label-width="150px">
          <el-form-item
            v-for="field in formFields"
            :key="field.field"
            :label="field.title"
          >
            <el-input
              v-model="formData[field.field]"
              :placeholder="field.props.placeholder || '请输入'"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格字段 - 使用 StationConfigTable -->
      <div v-if="tableFields.length > 0" class="table-section">
        <h5>表格信息 (StationConfigTable)</h5>
        <div
          v-for="tableField in tableFields"
          :key="tableField.field"
          class="table-item"
        >
          <h6>{{ tableField.title }}</h6>
          <p v-if="tableField.description" class="table-description">
            {{ tableField.description }}
          </p>
          <StationConfigTable
            :ref="tableField.field"
            :columns="getTableColumns(tableField.tableType)"
            v-model="formData[tableField.field]"
            showAddBtn
          />
        </div>
      </div>

      <!-- 表格字段提示 - 原始版本作为对比 -->
      <div v-if="tableFields.length > 0" class="table-section">
        <h5>表格信息 (原始提示)</h5>
        <div v-for="tableField in tableFields" :key="tableField.field">
          <p>{{ tableField.title }}: {{ tableField.description }}</p>
          <p>
            表格数据: {{ JSON.stringify(formData[tableField.field] || []) }}
          </p>
        </div>
      </div>

      <!-- 调试信息 -->
      <div
        class="debug-section"
        style="margin-top: 20px; padding: 10px; background: #f5f5f5;"
      >
        <h5>调试信息</h5>
        <p>contractType: {{ contractType }}</p>
        <p>formData: {{ JSON.stringify(formData) }}</p>
        <p>isUpdating: {{ isUpdating }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getConfigByType,
  DICT_TYPES,
  getTableColumns,
} from "./otherInfoConfig.js";
import StationConfigTable from "@/components/StationConfigTable/index.vue";

export default {
  name: "ContractOtherInfoSimple",
  components: {
    StationConfigTable,
  },
  props: {
    // 合同协议类型
    contractType: {
      type: String,
      default: "",
    },
    // 表单数据，支持 v-model
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
      isUpdating: false,
      // 字典数据
      areaOptions: [],
      electricityBillingMethodOptions: [],
      hasLateFeeOptions: [],
      revenueModeOptions: [],
      agreementTypeOptions: [],
    };
  },
  computed: {
    // 当前类型配置
    currentConfig() {
      return getConfigByType(this.contractType);
    },

    // 表单字段
    formFields() {
      return this.currentConfig.formFields || [];
    },

    // 表格字段
    tableFields() {
      return this.currentConfig.tableFields || [];
    },
  },
  watch: {
    // 监听 contractType 变化
    contractType: {
      handler(newType, oldType) {
        console.log("contractType changed:", newType, "from:", oldType);
        if (newType !== oldType) {
          this.initFormData();
        }
      },
      immediate: true,
    },

    // 监听 value 变化
    value: {
      handler(newValue) {
        console.log("value changed:", newValue);
        if (!this.isUpdating && newValue) {
          this.formData = { ...newValue };
        }
      },
      immediate: true,
      deep: true,
    },

    // 监听内部数据变化
    formData: {
      handler(newData) {
        console.log("formData changed:", newData);
        if (!this.isUpdating) {
          this.isUpdating = true;
          this.$nextTick(() => {
            this.$emit("input", newData);
            this.isUpdating = false;
          });
        }
      },
      deep: true,
    },
  },
  created() {
    console.log("ContractOtherInfoSimple created");
    this.loadDictData();
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      console.log("initFormData called, contractType:", this.contractType);

      if (!this.contractType) {
        this.isUpdating = true;
        this.formData = {};
        this.$nextTick(() => {
          this.isUpdating = false;
        });
        return;
      }

      const config = this.currentConfig;
      console.log("config:", config);

      if (!config || config.name === "未知类型") {
        console.warn("Invalid config for contractType:", this.contractType);
        return;
      }

      const initialData = {};

      // 初始化表单字段
      if (config.formFields && Array.isArray(config.formFields)) {
        config.formFields.forEach((field) => {
          initialData[field.field] = "";
        });
      }

      // 初始化表格字段
      if (config.tableFields && Array.isArray(config.tableFields)) {
        config.tableFields.forEach((field) => {
          initialData[field.field] = [];
        });
      }

      console.log("initialData:", initialData);

      this.isUpdating = true;
      this.formData = {
        ...initialData,
        ...this.value,
      };

      this.$nextTick(() => {
        this.isUpdating = false;
        console.log("formData updated:", this.formData);
      });
    },

    // 表单验证
    async validate() {
      console.log("validate called");
      return true; // 简化版本直接返回 true
    },

    // 重置表单
    resetForm() {
      console.log("resetForm called");
      this.initFormData();
    },

    // 加载字典数据
    loadDictData() {
      console.log("loadDictData called");

      // 检查 getDicts 方法是否存在
      if (typeof this.getDicts !== "function") {
        console.warn("getDicts method not found, using mock data");
        // 使用模拟数据
        this.areaOptions = [
          { dictLabel: "华北区域", dictValue: "north" },
          { dictLabel: "华南区域", dictValue: "south" },
        ];
        this.electricityBillingMethodOptions = [
          { dictLabel: "峰谷电价", dictValue: "peak_valley" },
          { dictLabel: "固定电价", dictValue: "fixed" },
        ];
        this.hasLateFeeOptions = [
          { dictLabel: "是", dictValue: "1" },
          { dictLabel: "否", dictValue: "0" },
        ];
        this.revenueModeOptions = [
          { dictLabel: "分成模式", dictValue: "share" },
          { dictLabel: "租赁模式", dictValue: "rent" },
        ];
        this.agreementTypeOptions = [
          { dictLabel: "框架协议", dictValue: "framework" },
          { dictLabel: "具体协议", dictValue: "specific" },
        ];
        return;
      }

      console.log("开始加载真实字典数据");

      // 加载区域配置
      this.getDicts(DICT_TYPES.AREA)
        .then((response) => {
          console.log("区域数据加载成功:", response);
          this.areaOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load area options:", error);
          this.areaOptions = [];
        });

      // 加载电费计费方式
      this.getDicts(DICT_TYPES.ELECTRICITY_BILLING_METHOD)
        .then((response) => {
          console.log("电费计费方式数据加载成功:", response);
          this.electricityBillingMethodOptions = response.data || [];
        })
        .catch((error) => {
          console.error(
            "Failed to load electricity billing method options:",
            error
          );
          this.electricityBillingMethodOptions = [];
        });

      // 加载是否有滞纳金
      this.getDicts(DICT_TYPES.HAS_LATE_FEE)
        .then((response) => {
          console.log("滞纳金数据加载成功:", response);
          this.hasLateFeeOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load late fee options:", error);
          this.hasLateFeeOptions = [];
        });

      // 加载收益模式
      this.getDicts(DICT_TYPES.REVENUE_MODE)
        .then((response) => {
          console.log("收益模式数据加载成功:", response);
          this.revenueModeOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load revenue mode options:", error);
          this.revenueModeOptions = [];
        });

      // 加载协议类型
      this.getDicts(DICT_TYPES.AGREEMENT_TYPE)
        .then((response) => {
          console.log("协议类型数据加载成功:", response);
          this.agreementTypeOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load agreement type options:", error);
          this.agreementTypeOptions = [];
        });

      console.log("所有字典数据加载请求已发送");
    },

    // 获取表格列配置
    getTableColumns(tableType) {
      console.log("getTableColumns called with:", tableType);

      const columns = getTableColumns(tableType);
      console.log("原始列配置:", columns);

      // 动态设置字典数据（逐步恢复完整功能）
      const processedColumns = columns.map((column) => {
        const newColumn = { ...column };

        console.log("处理列:", column.field, column.element);

        // 设置区域选项
        if (column.field === "area") {
          console.log("设置区域选项:", this.areaOptions);
          newColumn.props = {
            ...column.props,
            fetchSuggestions: this.queryAreaSearch,
          };
        }

        // 设置字典选项
        if (column.field === "electricityBillingMethod") {
          console.log(
            "设置电费计费方式选项:",
            this.electricityBillingMethodOptions
          );
          newColumn.props = {
            ...column.props,
            options: this.electricityBillingMethodOptions,
          };
        }

        if (column.field === "hasLateFee") {
          console.log("设置滞纳金选项:", this.hasLateFeeOptions);
          newColumn.props = {
            ...column.props,
            options: this.hasLateFeeOptions,
          };
        }

        if (column.field === "revenueMode") {
          console.log("设置收益模式选项:", this.revenueModeOptions);
          newColumn.props = {
            ...column.props,
            options: this.revenueModeOptions,
          };
        }

        console.log("处理后的列:", newColumn.field, newColumn.props);
        return newColumn;
      });

      console.log("最终列配置:", processedColumns);
      return processedColumns;
    },

    // 区域搜索
    queryAreaSearch(queryString, cb) {
      console.log("queryAreaSearch called:", queryString);
      const results = this.areaOptions
        .filter((item) => {
          return item.dictLabel
            .toLowerCase()
            .includes(queryString.toLowerCase());
        })
        .map((item) => ({
          value: item.dictLabel,
        }));
      console.log("搜索结果:", results);
      cb(results);
    },
  },
};
</script>

<style lang="less" scoped>
.contract-other-info-simple {
  .no-type-message {
    padding: 20px;
    text-align: center;
    color: #909399;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .form-section,
  .table-section {
    margin-top: 20px;

    h5 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    h6 {
      margin: 10px 0 5px 0;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
    }

    .table-item {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      background: #fafafa;
    }

    .table-description {
      margin: 0 0 10px 0;
      font-size: 12px;
      color: #909399;
    }
  }

  .debug-section {
    h5 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #606266;
    }

    p {
      margin: 5px 0;
      font-size: 12px;
      color: #909399;
      word-break: break-all;
    }
  }
}
</style>
