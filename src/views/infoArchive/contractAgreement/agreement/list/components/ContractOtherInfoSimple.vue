<!-- 合同协议其他信息组件 - 简化版本 -->
<template>
  <div class="contract-other-info-simple">
    <div v-if="!contractType" class="no-type-message">
      请先选择合同协议类型
    </div>

    <div v-else>
      <h4>合同协议类型: {{ contractType }}</h4>
      <p>配置名称: {{ currentConfig.name }}</p>

      <!-- 基础表单字段 - 使用 DynamicForm -->
      <div v-if="formFields.length > 0" class="form-section">
        <h5>基础信息 (DynamicForm)</h5>
        <DynamicForm
          ref="dynamicForm"
          :config="formFields"
          :params="formData"
          :defaultColSpan="24"
          labelPosition="right"
          labelWidth="150px"
        />
      </div>

      <!-- 基础表单字段 - 原始版本作为对比 -->
      <div v-if="formFields.length > 0" class="form-section">
        <h5>基础信息 (原始表单)</h5>
        <el-form :model="formData" label-width="150px">
          <el-form-item
            v-for="field in formFields"
            :key="field.field"
            :label="field.title"
          >
            <el-input
              v-model="formData[field.field]"
              :placeholder="field.props.placeholder || '请输入'"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格字段提示 -->
      <div v-if="tableFields.length > 0" class="table-section">
        <h5>表格信息</h5>
        <div v-for="tableField in tableFields" :key="tableField.field">
          <p>{{ tableField.title }}: {{ tableField.description }}</p>
          <p>
            表格数据: {{ JSON.stringify(formData[tableField.field] || []) }}
          </p>
        </div>
      </div>

      <!-- 调试信息 -->
      <div
        class="debug-section"
        style="margin-top: 20px; padding: 10px; background: #f5f5f5;"
      >
        <h5>调试信息</h5>
        <p>contractType: {{ contractType }}</p>
        <p>formData: {{ JSON.stringify(formData) }}</p>
        <p>isUpdating: {{ isUpdating }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { getConfigByType, DICT_TYPES } from "./otherInfoConfig.js";

export default {
  name: "ContractOtherInfoSimple",
  props: {
    // 合同协议类型
    contractType: {
      type: String,
      default: "",
    },
    // 表单数据，支持 v-model
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
      isUpdating: false,
      // 字典数据
      areaOptions: [],
      electricityBillingMethodOptions: [],
      hasLateFeeOptions: [],
      revenueModeOptions: [],
      agreementTypeOptions: [],
    };
  },
  computed: {
    // 当前类型配置
    currentConfig() {
      return getConfigByType(this.contractType);
    },

    // 表单字段
    formFields() {
      return this.currentConfig.formFields || [];
    },

    // 表格字段
    tableFields() {
      return this.currentConfig.tableFields || [];
    },
  },
  watch: {
    // 监听 contractType 变化
    contractType: {
      handler(newType, oldType) {
        console.log("contractType changed:", newType, "from:", oldType);
        if (newType !== oldType) {
          this.initFormData();
        }
      },
      immediate: true,
    },

    // 监听 value 变化
    value: {
      handler(newValue) {
        console.log("value changed:", newValue);
        if (!this.isUpdating && newValue) {
          this.formData = { ...newValue };
        }
      },
      immediate: true,
      deep: true,
    },

    // 监听内部数据变化
    formData: {
      handler(newData) {
        console.log("formData changed:", newData);
        if (!this.isUpdating) {
          this.isUpdating = true;
          this.$nextTick(() => {
            this.$emit("input", newData);
            this.isUpdating = false;
          });
        }
      },
      deep: true,
    },
  },
  created() {
    console.log("ContractOtherInfoSimple created");
    this.loadDictData();
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      console.log("initFormData called, contractType:", this.contractType);

      if (!this.contractType) {
        this.isUpdating = true;
        this.formData = {};
        this.$nextTick(() => {
          this.isUpdating = false;
        });
        return;
      }

      const config = this.currentConfig;
      console.log("config:", config);

      if (!config || config.name === "未知类型") {
        console.warn("Invalid config for contractType:", this.contractType);
        return;
      }

      const initialData = {};

      // 初始化表单字段
      if (config.formFields && Array.isArray(config.formFields)) {
        config.formFields.forEach((field) => {
          initialData[field.field] = "";
        });
      }

      // 初始化表格字段
      if (config.tableFields && Array.isArray(config.tableFields)) {
        config.tableFields.forEach((field) => {
          initialData[field.field] = [];
        });
      }

      console.log("initialData:", initialData);

      this.isUpdating = true;
      this.formData = {
        ...initialData,
        ...this.value,
      };

      this.$nextTick(() => {
        this.isUpdating = false;
        console.log("formData updated:", this.formData);
      });
    },

    // 表单验证
    async validate() {
      console.log("validate called");
      return true; // 简化版本直接返回 true
    },

    // 重置表单
    resetForm() {
      console.log("resetForm called");
      this.initFormData();
    },

    // 加载字典数据
    loadDictData() {
      console.log("loadDictData called");

      // 检查 getDicts 方法是否存在
      if (typeof this.getDicts !== "function") {
        console.warn("getDicts method not found, using mock data");
        // 使用模拟数据
        this.areaOptions = [
          { dictLabel: "华北区域", dictValue: "north" },
          { dictLabel: "华南区域", dictValue: "south" },
        ];
        this.electricityBillingMethodOptions = [
          { dictLabel: "峰谷电价", dictValue: "peak_valley" },
          { dictLabel: "固定电价", dictValue: "fixed" },
        ];
        return;
      }

      console.log("开始加载真实字典数据");

      // 加载区域配置
      this.getDicts(DICT_TYPES.AREA)
        .then((response) => {
          console.log("区域数据加载成功:", response);
          this.areaOptions = response.data || [];
        })
        .catch((error) => {
          console.error("Failed to load area options:", error);
          this.areaOptions = [];
        });

      // 加载电费计费方式
      this.getDicts(DICT_TYPES.ELECTRICITY_BILLING_METHOD)
        .then((response) => {
          console.log("电费计费方式数据加载成功:", response);
          this.electricityBillingMethodOptions = response.data || [];
        })
        .catch((error) => {
          console.error(
            "Failed to load electricity billing method options:",
            error
          );
          this.electricityBillingMethodOptions = [];
        });

      console.log("字典数据加载请求已发送");
    },
  },
};
</script>

<style lang="less" scoped>
.contract-other-info-simple {
  .no-type-message {
    padding: 20px;
    text-align: center;
    color: #909399;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .form-section,
  .table-section {
    margin-top: 20px;

    h5 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .debug-section {
    h5 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #606266;
    }

    p {
      margin: 5px 0;
      font-size: 12px;
      color: #909399;
      word-break: break-all;
    }
  }
}
</style>
