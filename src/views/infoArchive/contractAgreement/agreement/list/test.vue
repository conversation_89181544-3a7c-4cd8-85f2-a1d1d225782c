<!-- 测试页面 -->
<template>
  <div class="test-page">
    <h2>ContractOtherInfo 组件测试</h2>
    
    <el-card>
      <h3>测试控制</h3>
      <el-form inline>
        <el-form-item label="合同协议类型:">
          <el-select v-model="testContractType" placeholder="请选择" @change="handleTypeChange">
            <el-option label="无类型" value=""></el-option>
            <el-option label="投建协议申请记录-充电" value="1"></el-option>
            <el-option label="投建协议申请记录-储能" value="2"></el-option>
            <el-option label="合同申请记录" value="3"></el-option>
            <el-option label="协议申请记录" value="4"></el-option>
            <el-option label="采购框架协议申请" value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetData">重置数据</el-button>
          <el-button @click="validateForm">验证表单</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <h3>组件测试区域</h3>
      <ContractOtherInfoSimple
        ref="testComponent"
        :contractType="testContractType"
        v-model="testData"
        @input="handleDataChange"
      />
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <h3>数据监控</h3>
      <div class="data-monitor">
        <h4>当前数据:</h4>
        <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
        
        <h4>类型变化日志:</h4>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            {{ log }}
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import ContractOtherInfoSimple from "./components/ContractOtherInfoSimple.vue";

export default {
  name: "ContractOtherInfoTest",
  components: {
    ContractOtherInfoSimple,
  },
  data() {
    return {
      testContractType: "",
      testData: {},
      logs: [],
    };
  },
  methods: {
    handleTypeChange(newType) {
      this.addLog(`合同协议类型变更: ${newType}`);
      console.log("Type changed to:", newType);
    },
    
    handleDataChange(newData) {
      this.addLog(`数据变化: ${JSON.stringify(newData)}`);
      console.log("Data changed:", newData);
    },
    
    resetData() {
      this.testData = {};
      this.addLog("数据已重置");
    },
    
    async validateForm() {
      try {
        const result = await this.$refs.testComponent.validate();
        this.addLog(`验证结果: ${result}`);
        this.$message.success("验证成功");
      } catch (error) {
        this.addLog(`验证失败: ${error.message}`);
        this.$message.error("验证失败");
      }
    },
    
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.unshift(`[${timestamp}] ${message}`);
      // 只保留最近20条日志
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20);
      }
    },
  },
  
  created() {
    this.addLog("测试页面已创建");
  },
};
</script>

<style lang="less" scoped>
.test-page {
  padding: 20px;
  
  .data-monitor {
    pre {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .log-container {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 10px;
      background: #fafafa;
      
      .log-item {
        font-size: 12px;
        color: #606266;
        margin-bottom: 5px;
        padding: 2px 0;
        border-bottom: 1px solid #ebeef5;
        
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
