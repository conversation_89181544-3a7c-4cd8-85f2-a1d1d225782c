import { getToken } from "@/utils/auth";

export default {
  data() {
    return { token: "" };
  },
  created() {
    this.token = getToken();
  },
  methods: {
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleCommonExport(apiFn, params, cusText) {
      let text = cusText || "是否确认导出所有数据?";
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        apiFn(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          // this.reportTrackEvent(DATA_INFO_CLICK_CHARING_GUN_REPORT);
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
  },
};
