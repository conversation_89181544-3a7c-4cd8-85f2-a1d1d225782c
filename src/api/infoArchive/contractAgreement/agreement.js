import request from "@/utils/request";
//协议档案
export default {
  // 列表
  list(data) {
    return request({
      url: "/contractInfo/queryList",
      method: "post",
      data,
    });
  },
  //获取运管单号下拉
  remoteOmOptions(data) {
    return request({
      url: "/contractInfo/queryList",
      method: "post",
      data,
    });
  },
  //邦定运管单号
  bind(data) {
    return request({
      url: "/contractInfo/remark",
      method: "post",
      data,
    });
  },
  //变更
  change(data) {
    return request({
      url: "/contractInfo/remark",
      method: "post",
      data,
    });
  },
  // 备注
  remark(data) {
    return request({
      url: "/contractInfo/remark",
      method: "post",
      data,
    });
  },
  //导出
  export(data) {
    return request({
      url: "/contractInfo/export",
      method: "post",
      data,
    });
  },
  //运管审批进度
  queryProcess(data) {
    return request({
      url: "/ledger/order/getApproveRecord",
      method: "get",
      params: data,
    });
  },
  //业务类型列表
  queryBusinessType(data) {
    return request({
      url: "/contractInfo/businessType",
      method: "get",
      params: data,
    });
  },
  //审批状态列表
  queryApplyStatus(data) {
    return request({
      url: "/contractInfo/applyStatus",
      method: "get",
      params: data,
    });
  },
  //场站名称列表
  queryStationName(data) {
    return request({
      url: "/contractInfo/stationName",
      method: "post",
      data,
    });
  },
  //合同协议方列表
  queryContractParty(data) {
    return request({
      url: "/contractInfo/contractParty",
      method: "post",
      data,
    });
  },
  //合同详情-基本信息
  baseInfoDetail(data) {
    return request({
      url: "/contractInfo/basicDetail",
      method: "get",
      params: data,
    });
  },
  //合同详情-预警信息
  queryWarnList(data) {
    return request({
      url: "/contractInfo/alarmDetail",
      method: "post",
      data,
    });
  },
  //合同详情-切换预警状态
  changeMonitorStatus(data) {
    return request({
      url: "/contractInfo/onOrOff",
      method: "post",
      data,
    });
  },
  //合同详情-关联项目列表
  associateProject(data) {
    return request({
      url: "/contractInfo/associatedProjects",
      method: "get",
      params: data,
    });
  },
  //合同详情-关联合同列表
  associateContract(data) {
    return request({
      url: "/project/batch/costList",
      method: "post",
      data,
    });
  },
  //合同详情-操作日志
  queryLog(data) {
    return request({
      url: "/contractInfo/log",
      method: "get",
      params: data,
    });
  },
};
