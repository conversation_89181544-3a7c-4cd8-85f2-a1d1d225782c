# ContractOtherInfo 组件调试指南

## 问题描述
选择合同协议类型后，整个页面和系统卡住不动。

## 可能的原因分析

### 1. 无限循环问题
**原因**: watch 监听器之间形成循环依赖
- `contractType` 变化 → `initFormData()` → 修改 `formData`
- `formData` 变化 → 触发 `formData` watch → `$emit("input")`
- 父组件接收数据 → 可能触发其他变化 → 再次触发 `contractType` 变化

**解决方案**: 
- 添加 `isUpdating` 标志防止循环
- 使用 `$nextTick` 确保更新顺序
- 简化 JSON 比较逻辑

### 2. 字典数据加载问题
**原因**: `getDicts` 方法可能不存在或返回错误
- 组件中调用了 `this.getDicts()` 但该方法可能未定义
- 字典数据加载失败导致组件卡死

**解决方案**:
- 添加 `getDicts` 方法存在性检查
- 添加错误处理 `.catch()`
- 提供默认空数组

### 3. 配置文件问题
**原因**: `otherInfoConfig.js` 中的配置可能有问题
- 配置结构不正确
- 字段定义有误
- 循环引用

## 调试步骤

### 1. 检查控制台输出
打开浏览器开发者工具，查看控制台是否有以下日志：
```
ContractOtherInfo created
contractType changed: [类型值] from: [旧值]
initFormData called, contractType: [类型值]
config: [配置对象]
initialData: [初始数据]
formData updated: [表单数据]
```

### 2. 检查错误信息
查看是否有以下错误：
- `getDicts method not found`
- `Failed to load [字典类型] options`
- 任何 JavaScript 错误

### 3. 检查网络请求
在 Network 标签中查看是否有：
- 字典数据请求失败
- 请求超时
- 无限重复的请求

## 临时解决方案

### 方案1: 禁用字典数据加载
临时注释掉 `loadDictData()` 方法的调用：
```javascript
created() {
  console.log("ContractOtherInfo created");
  // this.loadDictData(); // 临时注释
},
```

### 方案2: 简化组件
临时移除表格字段，只保留基础表单字段：
```javascript
// 在 otherInfoConfig.js 中临时清空 tableFields
tableFields: [], // 临时清空
```

### 方案3: 添加防护
在组件中添加更多防护逻辑：
```javascript
// 在 initFormData 开始添加
if (this.isUpdating) {
  console.log("Already updating, skip initFormData");
  return;
}
```

## 修复后的关键改动

### 1. 添加防抖机制
```javascript
data() {
  return {
    // ...
    isUpdating: false, // 防止无限循环
  };
}
```

### 2. 优化 watch 监听器
```javascript
formData: {
  handler(newData) {
    if (!this.isUpdating) {
      this.isUpdating = true;
      this.$nextTick(() => {
        this.$emit("input", newData);
        this.isUpdating = false;
      });
    }
  },
  deep: true,
}
```

### 3. 添加错误处理
```javascript
loadDictData() {
  if (typeof this.getDicts !== 'function') {
    console.warn('getDicts method not found');
    return;
  }
  // ... 其他代码
}
```

## 测试建议

### 1. 逐步测试
1. 先测试不选择合同协议类型的情况
2. 选择最简单的类型（如"合同申请记录"）
3. 逐步测试复杂类型

### 2. 监控性能
使用浏览器性能工具监控：
- CPU 使用率
- 内存使用情况
- 是否有无限循环

### 3. 日志调试
在关键位置添加 console.log：
- 组件生命周期
- watch 触发
- 方法调用
- 数据变化

## 如果问题仍然存在

### 1. 检查父组件
查看 `add.vue` 中是否有：
- 对 `otherParams` 的额外处理
- 其他可能触发循环的逻辑

### 2. 检查 DynamicForm 组件
确认 DynamicForm 组件是否：
- 正确处理 props 变化
- 没有内部循环逻辑

### 3. 检查 StationConfigTable 组件
确认表格组件是否：
- 正确处理列配置变化
- 没有性能问题

## 最终建议

如果问题持续存在，建议：
1. 创建一个最简化的测试版本
2. 逐步添加功能直到找到问题根源
3. 考虑重构组件架构，减少复杂的 watch 依赖
