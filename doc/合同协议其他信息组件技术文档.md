# 合同协议其他信息组件技术文档

## 概述

本文档描述了合同协议管理模块中【其他信息】组件的技术实现，包括组件设计、数据结构、配置方式和使用方法。该组件支持根据合同协议类型动态显示不同的表单项，同时满足编辑和展示两种使用场景。

## 技术栈

- **前端框架**: Vue 2.x
- **UI组件库**: Element UI
- **表单组件**: DynamicForm（项目自定义组件）
- **表格组件**: StationConfigTable（项目自定义组件）
- **展示组件**: BaseDescriptions（项目自定义组件）
- **表格展示**: VXE-Table

## 文件结构

```
src/views/infoArchive/contractAgreement/agreement/list/components/
├── ContractOtherInfo.vue           # 其他信息组件（新建）
├── otherInfoConfig.js              # 统一配置文件（新建）
├── baseInfo.vue                    # 基本信息展示页面（已修改）
└── config.js                       # 原有配置文件（保持不变）

src/views/infoArchive/contractAgreement/agreement/list/
└── add.vue                         # 新增/编辑页面（已修改）

src/api/infoArchive/contractAgreement/
└── agreement.js                    # API接口文件（已扩展）

doc/
└── 合同协议其他信息组件技术文档.md    # 本文档
```

## 组件设计

### 1. ContractOtherInfo 组件

#### Props
- `contractType`: 合同协议类型（String）
- `value`: 表单数据，支持 v-model（Object）

#### 功能特性
- 根据合同协议类型动态显示不同的表单项
- 支持基础表单字段和表格编辑字段
- 支持表单验证
- 支持 v-model 双向绑定
- 自动加载字典数据

#### 使用方式
```vue
<ContractOtherInfo
  ref="otherInfo"
  :contractType="baseParams.contractType"
  v-model="otherParams"
/>
```

### 2. 统一配置文件 (otherInfoConfig.js)

#### 配置结构
```javascript
export const CONTRACT_TYPE_CONFIG = {
  "1": {  // 投建协议申请记录-充电
    name: "投建协议申请记录-充电",
    formFields: [...],      // 基础表单字段
    tableFields: [...],     // 表格字段
    displayFields: [...]    // 展示字段
  },
  // ... 其他类型配置
};
```

#### 字段类型支持
- **基础表单字段**: 输入框、下拉选择、日期选择等
- **表格字段**: 使用 StationConfigTable 组件的可编辑表格
- **展示字段**: 用于只读展示的字段配置

## 合同协议类型配置

### 支持的类型

1. **投建协议申请记录-充电** (dictValue: "1")
   - 基础字段: 渠道信息、项目名称、在建工程编码
   - 表格字段: 场所信息表（场所名称、场所编码、区域等）

2. **投建协议申请记录-储能** (dictValue: "2")
   - 基础字段: 渠道信息、项目名称、在建工程编码
   - 表格字段: 场所信息表（同充电类型）

3. **合同申请记录** (dictValue: "3")
   - 基础字段: 合同金额/比例

4. **协议申请记录** (dictValue: "4")
   - 基础字段: 协议类型（字典选择）

5. **采购框架协议申请** (dictValue: "5")
   - 基础字段: 商务政策(金额/分润比例)

### 表格字段配置

#### 投建协议场所信息表
包含以下字段：
- 场所名称（文本输入）
- 场所编码（文本输入）
- 区域（自动完成输入）
- 场地租赁费/分成比例（数字输入，3位小数）
- 电表户号归属方（文本输入）
- 电费计费方式（字典选择）
- 电费计费标准（数字输入，4位小数）
- 是否有滞纳金（字典选择）
- 收益模式（字典选择）
- 直流/交流桩数（整数输入）
- 枪数（整数输入）

## 字典数据

### 使用的字典类型
```javascript
export const DICT_TYPES = {
  AREA: 'area_config',                        // 区域配置
  ELECTRICITY_BILLING_METHOD: 'electricity_billing_method', // 电费计费方式
  HAS_LATE_FEE: 'has_late_fee',              // 是否有滞纳金
  REVENUE_MODE: 'revenue_mode',               // 收益模式
  AGREEMENT_TYPE: 'agreement_type',           // 协议类型
};
```

## 使用场景

### 1. 编辑场景 (add.vue)

在新增/编辑页面中使用：
```vue
<!-- 其他信息 -->
<el-card id="otherInfo" class="mt20">
  <div slot="header" class="card-title-wrap">
    <div class="card-title-line"></div>
    <span>其他信息</span>
  </div>
  <ContractOtherInfo
    ref="otherInfo"
    :contractType="baseParams.contractType"
    v-model="otherParams"
  />
</el-card>
```

#### 表单验证
```javascript
// 其他信息验证
const otherValid = await this.$refs.otherInfo.validate().catch(() => false);
if (!otherValid) {
  return;
}
```

### 2. 展示场景 (baseInfo.vue)

在详情页面中展示：
```vue
<!-- 新的其他信息展示卡片 -->
<el-card v-if="showNewOtherInfo">
  <CommonTitle class="mb10" :title="`${currentTypeConfig.name} - 详细信息`" />
  
  <!-- 基础字段展示 -->
  <BaseDescriptions :list="newOtherInfoList" :column="2">
    <template #links="{itemVal}">
      <FileIcons :list="itemVal"></FileIcons>
    </template>
  </BaseDescriptions>
  
  <!-- 表格字段展示 -->
  <div v-for="tableField in currentTypeConfig.tableFields" :key="tableField.field">
    <h4>{{ tableField.title }}</h4>
    <vxe-grid
      :columns="getDisplayTableColumns(tableField.tableType)"
      :data="baseInfo[tableField.field] || []"
    ></vxe-grid>
  </div>
</el-card>
```

## API接口

### 新增接口
1. `queryBusinessType`: 查询业务类型
2. `queryContractParty`: 查询合同协议方

### 字典接口
使用现有的 `getDicts` 方法获取字典数据

## 数据流

### 编辑模式数据流
1. 用户选择合同协议类型
2. 组件根据类型加载对应配置
3. 初始化表单数据结构
4. 用户填写表单数据
5. 通过 v-model 同步到父组件
6. 提交时包含在整体数据中

### 展示模式数据流
1. 从后端获取合同详情数据
2. 根据合同类型获取配置
3. 映射数据到展示字段
4. 渲染只读展示界面

## 扩展指南

### 添加新的合同协议类型

1. 在 `otherInfoConfig.js` 中添加新的类型配置：
```javascript
"6": {
  name: "新类型名称",
  formFields: [
    {
      field: "newField",
      title: "新字段",
      element: "el-input",
      props: { placeholder: "请输入" },
      rules: []
    }
  ],
  tableFields: [],
  displayFields: [
    { title: "新字段", value: "newField" }
  ]
}
```

2. 如需新的表格类型，在 `getTableColumns` 函数中添加配置

3. 更新字典数据（如需要）

### 添加新的字段类型

1. 在对应类型的 `formFields` 中添加字段配置
2. 在 `displayFields` 中添加展示配置
3. 如需特殊处理，在组件中添加相应逻辑

## 注意事项

1. **数据一致性**: 确保编辑和展示使用相同的字段配置
2. **字典数据**: 及时更新字典数据，确保下拉选项正确
3. **表单验证**: 根据业务需求配置合适的验证规则
4. **性能优化**: 大量数据时考虑分页或虚拟滚动
5. **权限控制**: 根据用户权限控制字段的可见性和可编辑性

## 最佳实践

1. **配置复用**: 相同字段在不同类型中复用配置
2. **数据验证**: 前端验证与后端验证相结合
3. **用户体验**: 提供清晰的字段说明和错误提示
4. **代码维护**: 保持配置文件的清晰结构和注释
